<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Viewer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8fafc;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border-left: 4px solid #2563eb;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2563eb;
        }
        .filters {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .filter-btn:hover, .filter-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        .items {
            padding: 20px;
        }
        .item {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
            transition: all 0.2s;
        }
        .item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .item-header {
            padding: 15px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }
        .item-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .item-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 500;
            text-transform: uppercase;
        }
        .type-milestone { background: #dbeafe; color: #1d4ed8; }
        .type-architecturaldecision { background: #fef3c7; color: #d97706; }
        .type-specification { background: #d1fae5; color: #059669; }
        .type-domain { background: #e0e7ff; color: #5b21b6; }
        .item-content {
            padding: 15px;
        }
        .item-description {
            color: #6b7280;
            margin-bottom: 10px;
        }
        .item-meta {
            display: flex;
            gap: 15px;
            font-size: 0.875em;
            color: #9ca3af;
        }
        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Knowledge Graph Viewer</h1>
            <p>Visual representation of your project documentation</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-count">0</div>
                <div>Total Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="milestone-count">0</div>
                <div>Milestones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="adr-count">0</div>
                <div>ADRs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="spec-count">0</div>
                <div>Specifications</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" id="search" placeholder="Search by title, description, or tags...">
            <div class="filter-buttons">
                <button class="filter-btn active" data-type="all">All</button>
                <button class="filter-btn" data-type="Milestone">Milestones</button>
                <button class="filter-btn" data-type="ArchitecturalDecision">ADRs</button>
                <button class="filter-btn" data-type="Specification">Specifications</button>
                <button class="filter-btn" data-type="Domain">Domains</button>
            </div>
        </div>

        <div class="items" id="items-container">
            <!-- Items will be loaded here -->
        </div>
    </div>

    <script>
        let allItems = [];
        let currentFilter = 'all';

        // Load and parse the knowledge graph
        async function loadKnowledgeGraph() {
            try {
                const response = await fetch('./kg.jsonld');
                const data = await response.json();
                allItems = data['@graph'] || [];
                updateStats();
                renderItems();
            } catch (error) {
                console.error('Error loading knowledge graph:', error);
                document.getElementById('items-container').innerHTML = 
                    '<p style="text-align: center; color: #ef4444;">Error loading knowledge graph. Make sure kg.jsonld exists.</p>';
            }
        }

        function updateStats() {
            const milestones = allItems.filter(item => item['@type'] === 'Milestone').length;
            const adrs = allItems.filter(item => item['@type'] === 'ArchitecturalDecision').length;
            const specs = allItems.filter(item => item['@type'] === 'Specification').length;
            
            document.getElementById('total-count').textContent = allItems.length;
            document.getElementById('milestone-count').textContent = milestones;
            document.getElementById('adr-count').textContent = adrs;
            document.getElementById('spec-count').textContent = specs;
        }

        function renderItems() {
            const container = document.getElementById('items-container');
            const searchTerm = document.getElementById('search').value.toLowerCase();
            
            let filteredItems = allItems;
            
            // Apply type filter
            if (currentFilter !== 'all') {
                filteredItems = filteredItems.filter(item => item['@type'] === currentFilter);
            }
            
            // Apply search filter
            if (searchTerm) {
                filteredItems = filteredItems.filter(item => {
                    const title = (item.title || '').toLowerCase();
                    const description = (item.description || '').toLowerCase();
                    const tags = (item.tags || []).join(' ').toLowerCase();
                    return title.includes(searchTerm) || description.includes(searchTerm) || tags.includes(searchTerm);
                });
            }

            container.innerHTML = filteredItems.map(item => `
                <div class="item">
                    <div class="item-header">
                        <div class="item-title">${item.title || 'Untitled'}</div>
                        <span class="item-type type-${item['@type'].toLowerCase()}">${item['@type']}</span>
                    </div>
                    <div class="item-content">
                        ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                        <div class="item-meta">
                            <span>📁 ${item.filePath || 'Unknown path'}</span>
                            ${item.status ? `<span>📊 ${item.status}</span>` : ''}
                            ${item.version ? `<span>🏷️ v${item.version}</span>` : ''}
                            ${item.tags ? `<span>🏷️ ${item.tags.join(', ')}</span>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', loadKnowledgeGraph);
        
        document.getElementById('search').addEventListener('input', renderItems);
        
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentFilter = btn.dataset.type;
                renderItems();
            });
        });
    </script>
</body>
</html>
