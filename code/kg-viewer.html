<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Viewer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8fafc;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border-left: 4px solid #2563eb;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2563eb;
        }
        .filters {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .filter-btn:hover, .filter-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        .items {
            padding: 20px;
        }
        .item {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
            transition: all 0.2s;
        }
        .item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .item-header {
            padding: 15px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }
        .item-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .item-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 500;
            text-transform: uppercase;
        }
        .type-milestone { background: #dbeafe; color: #1d4ed8; }
        .type-architecturaldecision { background: #fef3c7; color: #d97706; }
        .type-specification { background: #d1fae5; color: #059669; }
        .type-domain { background: #e0e7ff; color: #5b21b6; }
        .item-content {
            padding: 15px;
        }
        .item-description {
            color: #6b7280;
            margin-bottom: 10px;
        }
        .item-meta {
            display: flex;
            gap: 15px;
            font-size: 0.875em;
            color: #9ca3af;
        }
        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Knowledge Graph Viewer</h1>
            <p>Visual representation of your project documentation</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-count">0</div>
                <div>Total Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="milestone-count">0</div>
                <div>Milestones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="adr-count">0</div>
                <div>ADRs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="spec-count">0</div>
                <div>Specifications</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" id="search" placeholder="Search by title, description, or tags...">
            <div class="filter-buttons">
                <button class="filter-btn active" data-type="all">All</button>
                <button class="filter-btn" data-type="Milestone">Milestones</button>
                <button class="filter-btn" data-type="ArchitecturalDecision">ADRs</button>
                <button class="filter-btn" data-type="Specification">Specifications</button>
                <button class="filter-btn" data-type="Domain">Domains</button>
            </div>
        </div>

        <div class="items" id="items-container">
            <!-- Items will be loaded here -->
        </div>
    </div>

    <script>
        let allItems = [];
        let currentFilter = 'all';

        // Embedded knowledge graph data
        const knowledgeGraphData = {
            "@context": {
                "@vocab": "https://workflow-mapper.dev/vocab#",
                "title": "https://schema.org/name",
                "description": "https://schema.org/description",
                "status": "https://workflow-mapper.dev/vocab#status",
                "version": "https://schema.org/version",
                "created": "https://schema.org/dateCreated",
                "updated": "https://schema.org/dateModified",
                "tags": "https://schema.org/keywords",
                "authors": "https://schema.org/author",
                "filePath": "https://workflow-mapper.dev/vocab#filePath",
                "implements": "https://workflow-mapper.dev/vocab#implements",
                "dependsOn": "https://workflow-mapper.dev/vocab#dependsOn",
                "contains": "https://workflow-mapper.dev/vocab#contains"
            },
            "@graph": [
                {
                    "@id": "spec----docs-tech-specs-milestones-milestone-m0-1-mdx-milestone-m0-1---knowledge-graph-bootstrap",
                    "@type": "Milestone",
                    "filePath": "../docs/tech-specs/milestones/milestone-M0.1.mdx",
                    "title": "Milestone M0.1 — Knowledge-Graph Bootstrap",
                    "description": "Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI tools every agent can run.",
                    "created": "2025-01-25T00:00:00.000Z",
                    "version": "0.2.0",
                    "status": "Completed",
                    "tags": ["milestone", "knowledge-graph", "bootstrap"],
                    "authors": ["WorkflowMapper Team"]
                },
                {
                    "@id": "spec----docs-tech-specs-milestones-milestone-m0-2-mdx-milestone-m0-2---remove-agent-dry-run-references",
                    "@type": "Milestone",
                    "filePath": "../docs/tech-specs/milestones/milestone-M0.2.mdx",
                    "title": "Milestone M0.2 — Remove Agent Dry-Run References",
                    "description": "Clean up all traces of agent dry-run functionality from the repository, including package.json scripts, CI workflows, documentation, and work logs.",
                    "created": "2025-01-25T00:00:00.000Z",
                    "version": "0.1.0",
                    "status": "Draft",
                    "tags": ["milestone", "cleanup", "maintenance"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-milestones-milestone-m0-mdx-milestone-m0---repository-skeleton---ci",
                    "@type": "Milestone",
                    "filePath": "../docs/tech-specs/milestones/milestone-M0.mdx",
                    "title": "Milestone M0 — Repository Skeleton & CI",
                    "description": "The contractual scope, decisions, and acceptance tests for the very first deliverable.",
                    "created": "2025-05-25T00:00:00.000Z",
                    "updated": "2025-05-25T00:00:00.000Z",
                    "version": "0.5.0",
                    "status": "Completed",
                    "tags": ["milestone"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-milestones-milestone-m1-mdx-milestone-m1---static-graph-builder",
                    "@type": "Milestone",
                    "filePath": "../docs/tech-specs/milestones/milestone-M1.mdx",
                    "title": "Milestone M1 — Static Graph Builder",
                    "description": "Implements static code analysis and JSON-LD graph extraction for Python/JS using Tree-sitter.",
                    "created": "2024-05-25T00:00:00.000Z",
                    "version": "0.2.0",
                    "status": "Draft",
                    "tags": ["milestone"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-adrs-adr-001-monorepo-mdx-adr-001---monorepo-structure-with-pnpm-workspaces",
                    "@type": "ArchitecturalDecision",
                    "filePath": "../docs/tech-specs/adrs/adr-001-monorepo.mdx",
                    "title": "ADR-001 — Monorepo Structure with pnpm Workspaces",
                    "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.",
                    "created": "2025-05-25T00:00:00.000Z",
                    "updated": "2025-05-25T00:00:00.000Z",
                    "version": "1.0.0",
                    "status": "Accepted",
                    "tags": ["adr", "architecture", "monorepo"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-adrs-adr-002-typescript-mdx-adr-002---typescript-first-development",
                    "@type": "ArchitecturalDecision",
                    "filePath": "../docs/tech-specs/adrs/adr-002-typescript.mdx",
                    "title": "ADR-002 — TypeScript-First Development",
                    "description": "Decision to use strict TypeScript across frontend, backend, and shared code.",
                    "created": "2025-05-25T00:00:00.000Z",
                    "updated": "2025-05-25T00:00:00.000Z",
                    "version": "1.0.0",
                    "status": "Accepted",
                    "tags": ["adr", "architecture", "typescript"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-adrs-adr-003-jsonld-mdx-adr-003---json-ld-for-graph-representation",
                    "@type": "ArchitecturalDecision",
                    "filePath": "../docs/tech-specs/adrs/adr-003-jsonld.mdx",
                    "title": "ADR-003 — JSON-LD for Graph Representation",
                    "description": "Decision to use JSON-LD as the canonical format for representing workflow graphs.",
                    "created": "2025-05-25T00:00:00.000Z",
                    "updated": "2025-05-25T00:00:00.000Z",
                    "version": "1.0.0",
                    "status": "Accepted",
                    "tags": ["adr", "architecture", "data-format"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-process-agent-rules-augment-mdx-augment-agent-configuration",
                    "@type": "Specification",
                    "filePath": "../docs/tech-specs/process/agent-rules/augment.mdx",
                    "title": "Augment Agent Configuration",
                    "description": "Augment Agent specific configuration for milestone implementation",
                    "created": "2025-05-25",
                    "updated": "2025-05-25",
                    "version": "1.0.0",
                    "status": "Active",
                    "tags": ["agent-rules", "augment", "executable"],
                    "authors": ["nitishMehrotra"]
                },
                {
                    "@id": "spec----docs-tech-specs-process-agent-rules-core-mdx-core-agent-rules",
                    "@type": "Specification",
                    "filePath": "../docs/tech-specs/process/agent-rules/core.mdx",
                    "title": "Core Agent Rules",
                    "description": "Universal executable rules for all AI software engineering agents",
                    "created": "2025-05-25",
                    "updated": "2025-05-25",
                    "version": "1.0.0",
                    "status": "Active",
                    "tags": ["agent-rules", "core", "executable"],
                    "authors": ["nitishMehrotra"]
                }
            ]
        };

        // Load and parse the knowledge graph
        function loadKnowledgeGraph() {
            allItems = knowledgeGraphData['@graph'] || [];
            updateStats();
            renderItems();
        }

        function updateStats() {
            const milestones = allItems.filter(item => item['@type'] === 'Milestone').length;
            const adrs = allItems.filter(item => item['@type'] === 'ArchitecturalDecision').length;
            const specs = allItems.filter(item => item['@type'] === 'Specification').length;

            document.getElementById('total-count').textContent = allItems.length;
            document.getElementById('milestone-count').textContent = milestones;
            document.getElementById('adr-count').textContent = adrs;
            document.getElementById('spec-count').textContent = specs;
        }

        function renderItems() {
            const container = document.getElementById('items-container');
            const searchTerm = document.getElementById('search').value.toLowerCase();

            let filteredItems = allItems;

            // Apply type filter
            if (currentFilter !== 'all') {
                filteredItems = filteredItems.filter(item => item['@type'] === currentFilter);
            }

            // Apply search filter
            if (searchTerm) {
                filteredItems = filteredItems.filter(item => {
                    const title = (item.title || '').toLowerCase();
                    const description = (item.description || '').toLowerCase();
                    const tags = (item.tags || []).join(' ').toLowerCase();
                    return title.includes(searchTerm) || description.includes(searchTerm) || tags.includes(searchTerm);
                });
            }

            container.innerHTML = filteredItems.map(item => `
                <div class="item">
                    <div class="item-header">
                        <div class="item-title">${item.title || 'Untitled'}</div>
                        <span class="item-type type-${item['@type'].toLowerCase()}">${item['@type']}</span>
                    </div>
                    <div class="item-content">
                        ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                        <div class="item-meta">
                            <span>📁 ${item.filePath || 'Unknown path'}</span>
                            ${item.status ? `<span>📊 ${item.status}</span>` : ''}
                            ${item.version ? `<span>🏷️ v${item.version}</span>` : ''}
                            ${item.tags ? `<span>🏷️ ${item.tags.join(', ')}</span>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', loadKnowledgeGraph);

        document.getElementById('search').addEventListener('input', renderItems);

        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentFilter = btn.dataset.type;
                renderItems();
            });
        });
    </script>
</body>
</html>
